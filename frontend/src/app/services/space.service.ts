import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import {
  Space,
  SpaceType,
  SpaceStatus,
  Equipment,
  EquipmentType,
  EquipmentStatus,
  SpaceAvailability,
  CreateSpaceRequest,
  UpdateSpaceRequest,
  SpaceSearchFilters,
  SpaceReservation,
  ReservationStatus,
  CalendarEvent,
  CalendarResource
} from '../models/space.model';
import { RecurrenceType } from '../models/reservation.model';
import { SiteContextService } from './site-context.service';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class SpaceService {
  private readonly apiUrl = `${environment.apiUrl}/api/spaces`;

  private spacesSubject = new BehaviorSubject<Space[]>([]);
  public spaces$ = this.spacesSubject.asObservable();

  private reservationsSubject = new BehaviorSubject<SpaceReservation[]>([]);
  public reservations$ = this.reservationsSubject.asObservable();

  constructor(
    private http: HttpClient,
    private siteContext: SiteContextService
  ) {}

  /**
   * Get current site ID from context
   */
  private getCurrentSiteId(): string {
    const currentSite = this.siteContext.getCurrentSite();
    if (!currentSite || !currentSite.id) {
      console.warn('⚠️ No site selected in context, using fallback');
      return '1'; // Fallback to site ID 1
    }
    return currentSite.id;
  }

  // Méthodes CRUD pour les espaces
  getSpaces(): Observable<Space[]> {
    const params = new HttpParams().set('siteId', this.getCurrentSiteId());

    return this.http.get<any[]>(`${this.apiUrl}`, { params }).pipe(
      map(response => this.mapSpacesFromBackend(response)),
      catchError(error => {
        console.error('Error fetching spaces:', error);
        return throwError(() => error);
      })
    );
  }

  getActiveSpaces(): Observable<Space[]> {
    const params = new HttpParams()
      .set('siteId', this.getCurrentSiteId())
      .set('activeOnly', 'true');

    return this.http.get<any[]>(`${this.apiUrl}`, { params }).pipe(
      map(response => this.mapSpacesFromBackend(response)),
      catchError(error => {
        console.error('Error fetching active spaces:', error);
        return throwError(() => error);
      })
    );
  }

  getSpaceById(id: string): Observable<Space | null> {
    return this.http.get<any>(`${this.apiUrl}/${id}`).pipe(
      map(response => this.mapSpaceFromBackend(response)),
      catchError(error => {
        console.error('Error fetching space by id:', error);
        return throwError(() => error);
      })
    );
  }

  createSpace(request: CreateSpaceRequest): Observable<Space> {
    const params = new HttpParams().set('siteId', this.getCurrentSiteId());
    const body = this.mapCreateRequestToBackend(request);

    return this.http.post<any>(`${this.apiUrl}`, body, { params }).pipe(
      map(response => this.mapSpaceFromBackend(response)),
      catchError(error => {
        console.error('Error creating space:', error);
        return throwError(() => error);
      })
    );
  }

  updateSpace(request: UpdateSpaceRequest): Observable<Space> {
    const params = new HttpParams().set('siteId', this.getCurrentSiteId());
    const body = this.mapUpdateRequestToBackend(request);

    return this.http.put<any>(`${this.apiUrl}/${request.id}`, body, { params }).pipe(
      map(response => this.mapSpaceFromBackend(response)),
      catchError(error => {
        console.error('Error updating space:', error);
        return throwError(() => error);
      })
    );
  }

  deleteSpace(id: string): Observable<boolean> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`).pipe(
      map(() => true),
      catchError(error => {
        console.error('Error deleting space:', error);
        return throwError(() => error);
      })
    );
  }

  searchSpaces(filters: SpaceSearchFilters): Observable<Space[]> {
    let params = new HttpParams().set('siteId', this.getCurrentSiteId());

    if (filters.search && filters.search.trim()) {
      params = params.set('search', filters.search.trim());
    }
    if (filters.type) {
      params = params.set('type', filters.type);
    }
    if (filters.capacity) {
      params = params.set('minCapacity', filters.capacity.toString());
    }
    if (filters.location) {
      params = params.set('location', filters.location);
    }
    if (filters.floor) {
      params = params.set('floor', filters.floor);
    }

    return this.http.get<any[]>(`${this.apiUrl}/search`, { params }).pipe(
      map(response => this.mapSpacesFromBackend(response)),
      catchError(error => {
        console.error('Error searching spaces:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get spaces count for a site
   */
  getSpacesCount(siteId: number): Observable<number> {
    const params = new HttpParams().set('siteId', siteId.toString());

    return this.http.get<number>(`${this.apiUrl}/count`, { params }).pipe(
      catchError(error => {
        console.error('Error fetching spaces count:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get available spaces count for a site
   */
  getAvailableSpacesCount(siteId: number): Observable<number> {
    const params = new HttpParams().set('siteId', siteId.toString());

    return this.http.get<number>(`${this.apiUrl}/available-count`, { params }).pipe(
      catchError(error => {
        console.error('Error fetching available spaces count:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get total capacity for a site
   */
  getTotalCapacity(siteId: number): Observable<number> {
    const params = new HttpParams().set('siteId', siteId.toString());

    return this.http.get<Space[]>(`${this.apiUrl}`, { params }).pipe(
      map(spaces => spaces.reduce((total, space) => total + (space.capacity || 0), 0)),
      catchError(error => {
        console.error('Error fetching total capacity:', error);
        return throwError(() => error);
      })
    );
  }

  // Méthodes pour les réservations (à implémenter avec le backend des réservations)
  getReservations(): Observable<SpaceReservation[]> {
    // TODO: Implement with reservations backend
    return this.reservations$;
  }

  getReservationsBySpace(spaceId: string): Observable<SpaceReservation[]> {
    const params = new HttpParams()
      .set('siteId', this.getCurrentSiteId());

    return this.http.get<any[]>(`${environment.apiUrl}/api/reservations/by-space/${spaceId}`, { params }).pipe(
      map(response => this.mapReservationsFromBackend(response)),
      catchError(error => {
        console.error('Error fetching reservations by space:', error);
        return throwError(() => error);
      })
    );
  }

  // Méthodes pour le planning visuel
  getCalendarEvents(startDate: Date, endDate: Date): Observable<CalendarEvent[]> {
    // TODO: Implement with reservations backend
    return this.reservations$.pipe(
      map(reservations => {
        return reservations
          .filter(res => res.startTime >= startDate && res.endTime <= endDate)
          .map(res => ({
            id: res.id,
            title: `${res.spaceName} - ${res.userName}`,
            start: res.startTime,
            end: res.endTime,
            resourceId: res.spaceId,
            color: this.getStatusColor(res.status),
            status: res.status,
            attendees: res.attendees,
            userName: res.userName
          }));
      })
    );
  }

  getCalendarResources(): Observable<CalendarResource[]> {
    return this.getSpaces().pipe(
      map(spaces => {
        return spaces.map(space => ({
          id: space.id,
          title: space.name,
          type: space.type,
          capacity: space.capacity,
          location: space.location,
          status: space.status
        }));
      })
    );
  }

  // Méthodes de mapping entre frontend et backend
  private mapSpacesFromBackend(backendSpaces: any[]): Space[] {
    return backendSpaces.map(space => this.mapSpaceFromBackend(space));
  }

  private mapReservationsFromBackend(backendReservations: any[]): SpaceReservation[] {
    return backendReservations.map(reservation => this.mapReservationFromBackend(reservation));
  }

  private mapReservationFromBackend(backendReservation: any): SpaceReservation {
    return {
      id: backendReservation.id?.toString() || '',
      spaceId: backendReservation.spaceId?.toString() || '',
      spaceName: backendReservation.spaceName || '',
      userId: backendReservation.memberId?.toString() || '',
      userName: backendReservation.memberName || '',
      userEmail: backendReservation.memberEmail || '',
      startTime: new Date(backendReservation.startTime),
      endTime: new Date(backendReservation.endTime),
      purpose: backendReservation.purpose || '',
      status: this.mapReservationStatus(backendReservation.status),
      attendees: backendReservation.numberOfPeople || 1,
      numberOfPeople: backendReservation.numberOfPeople || 1,
      notes: backendReservation.notes || '',
      recurrence: this.mapRecurrenceType(backendReservation.recurrence),
      totalCost: backendReservation.totalCost || 0,
      createdAt: new Date(backendReservation.createdAt || Date.now()),
      updatedAt: new Date(backendReservation.updatedAt || Date.now())
    };
  }

  private mapReservationStatus(backendStatus: string): ReservationStatus {
    switch (backendStatus) {
      case 'PENDING': return ReservationStatus.PENDING;
      case 'CONFIRMED': return ReservationStatus.CONFIRMED;
      case 'CANCELLED': return ReservationStatus.CANCELLED;
      case 'COMPLETED': return ReservationStatus.COMPLETED;
      case 'NO_SHOW': return ReservationStatus.NO_SHOW;
      default: return ReservationStatus.PENDING;
    }
  }

  private mapRecurrenceType(backendRecurrence: string): RecurrenceType {
    switch (backendRecurrence) {
      case 'NONE': return 'none';
      case 'DAILY': return 'daily';
      case 'WEEKLY': return 'weekly';
      case 'MONTHLY': return 'monthly';
      default: return 'none';
    }
  }

  private mapSpaceFromBackend(backendSpace: any): Space {
    return {
      id: backendSpace.id?.toString() || '',
      name: backendSpace.name || '',
      description: backendSpace.description || null,
      type: backendSpace.type || null,
      capacity: backendSpace.capacity || null,
      location: backendSpace.location || null,
      floor: backendSpace.floor || null,
      area: backendSpace.area || null,
      status: backendSpace.status || SpaceStatus.AVAILABLE,
      isActive: backendSpace.isActive !== undefined ? backendSpace.isActive : true,
      equipment: this.mapEquipmentFromBackend(backendSpace.equipment || []),
      amenities: backendSpace.amenities || [],
      images: backendSpace.images || [],
      availability: this.mapAvailabilityFromBackend(backendSpace.availability),
      pricing: {
        hourlyRate: backendSpace.hourlyRate || null,
        dailyRate: backendSpace.dailyRate || null,
        weeklyRate: backendSpace.weeklyRate || null,
        monthlyRate: backendSpace.monthlyRate || null,
        currency: 'MAD',
        discounts: []
      },
      rules: backendSpace.rules || [],
      createdAt: backendSpace.createdAt ? new Date(backendSpace.createdAt) : new Date(),
      updatedAt: backendSpace.updatedAt ? new Date(backendSpace.updatedAt) : new Date()
    };
  }

  private mapCreateRequestToBackend(request: CreateSpaceRequest): any {
    return {
      name: request.name,
      description: request.description,
      type: request.type,
      capacity: request.capacity,
      location: request.location,
      floor: request.floor,
      area: request.area,
      amenities: request.amenities,
      rules: request.rules,
      images: [],
      hourlyRate: request.pricing?.hourlyRate || null,
      dailyRate: request.pricing?.dailyRate || null,
      weeklyRate: request.pricing?.weeklyRate || null,
      monthlyRate: request.pricing?.monthlyRate || null,
      equipment: request.equipment?.map(eq => ({
        name: eq.name,
        type: eq.type,
        brand: eq.brand,
        model: eq.model,
        quantity: eq.quantity,
        status: eq.status,
        description: eq.description
      })) || [],
      availability: request.availability ? {
        isActive: request.availability.isActive,
        advanceBookingDays: request.availability.advanceBookingDays,
        minBookingDuration: request.availability.minBookingDuration,
        maxBookingDuration: request.availability.maxBookingDuration,
        bufferTime: request.availability.bufferTime,
        weeklySchedule: JSON.stringify({
          monday: request.availability.schedule?.monday || { isOpen: false },
          tuesday: request.availability.schedule?.tuesday || { isOpen: false },
          wednesday: request.availability.schedule?.wednesday || { isOpen: false },
          thursday: request.availability.schedule?.thursday || { isOpen: false },
          friday: request.availability.schedule?.friday || { isOpen: false },
          saturday: request.availability.schedule?.saturday || { isOpen: false },
          sunday: request.availability.schedule?.sunday || { isOpen: false }
        }),
        exceptions: request.availability.exceptions ? JSON.stringify(request.availability.exceptions) : null
      } : null,
      isActive: true
    };
  }

  private mapUpdateRequestToBackend(request: UpdateSpaceRequest): any {
    const { id, ...updateData } = request;
    return {
      name: updateData.name,
      description: updateData.description,
      type: updateData.type,
      capacity: updateData.capacity,
      location: updateData.location,
      floor: updateData.floor,
      area: updateData.area,
      amenities: updateData.amenities,
      rules: updateData.rules,
      hourlyRate: updateData.pricing?.hourlyRate,
      dailyRate: updateData.pricing?.dailyRate,
      weeklyRate: updateData.pricing?.weeklyRate,
      monthlyRate: updateData.pricing?.monthlyRate,
      equipment: updateData.equipment?.map(eq => ({
        name: eq.name,
        type: eq.type,
        brand: eq.brand,
        model: eq.model,
        quantity: eq.quantity,
        status: eq.status,
        description: eq.description
      })) || [],
      availability: updateData.availability ? {
        isActive: updateData.availability.isActive,
        advanceBookingDays: updateData.availability.advanceBookingDays,
        minBookingDuration: updateData.availability.minBookingDuration,
        maxBookingDuration: updateData.availability.maxBookingDuration,
        bufferTime: updateData.availability.bufferTime,
        weeklySchedule: JSON.stringify({
          monday: updateData.availability.schedule?.monday || { isOpen: false },
          tuesday: updateData.availability.schedule?.tuesday || { isOpen: false },
          wednesday: updateData.availability.schedule?.wednesday || { isOpen: false },
          thursday: updateData.availability.schedule?.thursday || { isOpen: false },
          friday: updateData.availability.schedule?.friday || { isOpen: false },
          saturday: updateData.availability.schedule?.saturday || { isOpen: false },
          sunday: updateData.availability.schedule?.sunday || { isOpen: false }
        }),
        exceptions: updateData.availability.exceptions ? JSON.stringify(updateData.availability.exceptions) : null
      } : null
    };
  }

  private mapEquipmentFromBackend(backendEquipment: any[]): Equipment[] {
    if (!backendEquipment || !Array.isArray(backendEquipment)) {
      return [];
    }

    return backendEquipment.map(eq => ({
      id: eq.id?.toString() || '',
      name: eq.name || '',
      type: eq.type || null,
      brand: eq.brand || '',
      model: eq.model || '',
      quantity: eq.quantity || 1,
      status: eq.status || EquipmentStatus.WORKING,
      description: eq.description || ''
    }));
  }

  private mapAvailabilityFromBackend(backendAvailability: any): SpaceAvailability {
    if (!backendAvailability) {
      return this.getDefaultAvailability();
    }

    // Parse weekly schedule if it's a JSON string
    let schedule = this.getDefaultAvailability().schedule;
    if (backendAvailability.weeklySchedule) {
      try {
        const parsedSchedule = typeof backendAvailability.weeklySchedule === 'string'
          ? JSON.parse(backendAvailability.weeklySchedule)
          : backendAvailability.weeklySchedule;
        schedule = parsedSchedule;
      } catch (e) {
        console.warn('Failed to parse weekly schedule:', e);
      }
    }

    // Parse exceptions if it's a JSON string
    let exceptions = [];
    if (backendAvailability.exceptions) {
      try {
        exceptions = typeof backendAvailability.exceptions === 'string'
          ? JSON.parse(backendAvailability.exceptions)
          : backendAvailability.exceptions;
      } catch (e) {
        console.warn('Failed to parse exceptions:', e);
      }
    }

    return {
      isActive: backendAvailability.isActive !== undefined ? backendAvailability.isActive : true,
      schedule: schedule,
      exceptions: exceptions,
      advanceBookingDays: backendAvailability.advanceBookingDays || 30,
      minBookingDuration: backendAvailability.minBookingDuration || 60,
      maxBookingDuration: backendAvailability.maxBookingDuration || 480,
      bufferTime: backendAvailability.bufferTime || 15
    };
  }

  private getDefaultAvailability(): SpaceAvailability {
    return {
      isActive: true,
      schedule: {
        monday: { isOpen: true, openTime: '08:00', closeTime: '18:00', breaks: [] },
        tuesday: { isOpen: true, openTime: '08:00', closeTime: '18:00', breaks: [] },
        wednesday: { isOpen: true, openTime: '08:00', closeTime: '18:00', breaks: [] },
        thursday: { isOpen: true, openTime: '08:00', closeTime: '18:00', breaks: [] },
        friday: { isOpen: true, openTime: '08:00', closeTime: '18:00', breaks: [] },
        saturday: { isOpen: false, openTime: '09:00', closeTime: '17:00', breaks: [] },
        sunday: { isOpen: false, openTime: '09:00', closeTime: '17:00', breaks: [] }
      },
      exceptions: [],
      advanceBookingDays: 30,
      minBookingDuration: 60,
      maxBookingDuration: 480,
      bufferTime: 15
    };
  }

  private getDefaultPricing(): any {
    return {
      hourlyRate: 0,
      dailyRate: 0,
      weeklyRate: 0,
      monthlyRate: 0,
      currency: 'MAD',
      discounts: []
    };
  }

  private getStatusColor(status: ReservationStatus): string {
    switch (status) {
      case ReservationStatus.CONFIRMED:
        return '#52c41a';
      case ReservationStatus.PENDING:
        return '#faad14';
      case ReservationStatus.CANCELLED:
        return '#ff4d4f';
      case ReservationStatus.COMPLETED:
        return '#1890ff';
      case ReservationStatus.NO_SHOW:
        return '#f5222d';
      default:
        return '#d9d9d9';
    }
  }
}
